<template>
    <div>
        <adminheader
            :title="$t('navigations.guides')"></adminheader>
        <div class="mx-1 mr-2">
            <div class="w-full p-6">
                <template v-for="p in reportlist" :key="p.id">
                    <router-link 
                        v-if="hasScopes(p.scopes)" 
                        :to="p.path" 
                        class="group flex items-center p-4 mb-3 bg-white rounded-lg border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-200 hover:-translate-y-0.5"
                    >
                    <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg flex items-center justify-center group-hover:from-purple-200 group-hover:to-indigo-200 transition-colors duration-200">
                        <svgCollection 
                        :icon="p.icon" 
                        dclass="h-6 w-6 text-purple-600 group-hover:text-indigo-600 transition-colors duration-200"
                        ></svgCollection>
                    </div>  
                    <div class="ml-4 flex-1">
                        <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-700 transition-colors duration-200">
                        {{ p.title }}
                        </h3>
                    </div>                    
                    <div class="flex-shrink-0 ml-4">
                        <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                    </router-link>
                </template>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  components: {
    adminheader,
    svgCollection,
  },
  methods: {
    hasScopes (p: any) {
      if (!p) {
        return true
      } else {
        let g: boolean = false
        if (this.scopes) {
          if (Array.isArray(p)) {
            for (let k = 0; k < this.scopes.length; k++) {
              if (p.indexOf(this.scopes[k]) > -1) {
                return true
              }
            }
            return false
          } else {
            return this.scopes.indexOf(p) > -1
          }          
        }
        return g
      }
    },
  },
  computed: {
    scopes () {
      let g: any = auth2Store.getState().profile
      return (g && g.scopes) || []
    },
    reportlist () {
      return [
        {
          id: 'consolidate',
          title: 'Consolidate Invoice Guide',
          icon: 'documenttext',
          path: {name: 'consolidateGuide'}
        },
        {
          id: 'subscriptionfilterguide',
          title: 'Subscription Filter Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionfilterguide'}
        },
        {
          id: 'syncbillingguide',
          title: 'Sync Billing Guide',
          icon: 'documenttext',
          path: {name: 'syncBillsGuide'}
        },
        {
          id: 'subscriptiontroubleshootguide',
          title: 'Subscription Troubleshoot Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionTroubleshootGuide'}
        },
        {
          id: 'customerGuide',
          title: 'Customer Guide',
          icon: 'documenttext',
          path: {name: 'customerGuide'}
        },
        {
          id: 'installerGuide',
          title: 'Installer Guide',
          icon: 'documenttext',
          path: {name: 'installerGuide'}
        },
        {
          id: 'subscriptionGroupGuide',
          title: 'Adding Subscription Group Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionGroupGuide'}
        }
      ]
    }
  }
})
</script>