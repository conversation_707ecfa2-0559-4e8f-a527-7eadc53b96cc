<template>
    <div>
        <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="dob"
            :labelTitle="$t('payments.paymentdate')">
          <DatePicker
            v-model="item.paymentdate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
          <ErrTextCompt :errs="errs && errs.paymentdate"></ErrTextCompt>
        </FormItemCompt>

        <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1 align-top"
            labelFor="amount"
            :labelTitle="$t('payments.amount')">
            <TextInput
                name="amount"
                type="number"
                v-model="item.amount"
                :placeholder="$t('c.frontPlc') + $t('payments.amount')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.amount"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1 align-top"
            labelFor="platform"
            :labelTitle="$t('payments.platform')">
            <TextInput
                name="platform"
                v-model="item.platform"
                :placeholder="$t('c.frontPlc') + $t('payments.platform')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.platform"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1 align-top"
            labelFor="platformid"
            :labelTitle="$t('payments.platformid')">
            <TextInput
                name="platformid"
                v-model="item.platformid"
                :placeholder="$t('c.frontPlc') + $t('payments.platformid')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.platformid"></ErrTextCompt>
        </FormItemCompt>

        <FormItemCompt class="my-5"
          labelFor="attachments"
          :labelTitle="$t('payments.attachments')">
            <div>
                <ShowFiles
                    :files="attachments"
                    :removeFile="removeFile" />
            </div>
            <UploadInput
                :addFile="addFile"
                :basePath="basePath"
                :token="token">
            </UploadInput>
      </FormItemCompt>

        <FormItemCompt
            class="w-full inline-block px-1 align-top"
            labelFor="lastIP"
            :labelTitle="$t('payments.remark')">
            <TextareaInput
                name="remark"
                :rows="5"
                v-model="item.remark"
                :placeholder="$t('c.frontPlc') + $t('payments.remark')"
                defaultColor="blue"></TextareaInput>
        </FormItemCompt>
    </div>
    <div class="my-5">
        <div v-if="!item.id" class="w-full text-center mb-5 py-1 rounded cursor-pointer bg-purple-300 hover:bg-purple-500 hover:text-white" @click="paymentCreate">{{$t('payments.create')}}</div>
        <div v-if="item.id" class="w-full text-center mb-5 py-1 rounded cursor-pointer bg-red-300 hover:bg-red-500 hover:text-white" @click="paymentUpdate">{{$t('payments.update')}}</div>
        <div class="w-full text-center py-1 rounded cursor-pointer bg-yellow-300 hover:bg-yellow-500 hover:text-white" @click="cancelFunc">{{$t('c.back')}}</div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import TextareaInput from '../../../components/cvui/form/TextareaInput.vue'
import UploadInput from '@/components/cvui/form/Upload2.vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import { getFile, deleteFile, basePath } from '../../../api'
export default defineComponent({
    components: {
        PopupModal,
        DatePicker,
        FormItemCompt,
        ErrTextCompt,
        TextInput,
        TextareaInput,
        UploadInput,
        ShowFiles
    },
    props: {
        item: {
            type: Object,
            required: true
        }, 
        cancel: {
            type: Function
        },
        submitfunc: {
            type: Function
        },
        savesilentfunc: {
            type: Function
        },
        token: {
            type: String,
            required: true
        }
    },
    mounted () {
        this.loadAttachments()
    },
    methods: {
        validate () {
            let p: boolean = true
            this.errs = {
                paymentdate: undefined,
                amount: undefined
            }
            if (!this.item.amount) {
                this.errs["amount"] = this.$t('errs.invalidAmount')
                p = false
            } else {
                this.item.amount = parseFloat(this.item.amount)
                console.log(this.item.amount)
                if (this.item.amount <= 0) {
                    this.errs["amount"] = this.$t('errs.invalidAmount')
                    p = false 
                }
            }
            if (!this.item.paymentdate) {
                this.errs["paymentdate"] = this.$t('errs.paymentdateCannotBeEmpty')
                p = false
            }
            return p
        },
        addFile (p: any) {
            if (!this.item.attachments) {
            this.item.attachments = []
            }
            this.item.attachments.push(p.file.id)
            this.attachments.push(p.file)
            this.savesilent()
        },
        savesilent () {
            if (this.savesilentfunc) {
                this.savesilentfunc(this.item)
            }
        },
        removeFile (p: any) {
            let i = this.item.attachments.indexOf(p)
            if (i > -1) {
                this.item.attachments.splice(i, 1)
                this.savesilent()
            }
            let list = this.attachments.map((p: any) => p.id || p.ID)
            let j = list.indexOf(p)
            this.attachments.splice(j, 1)
            deleteFile({id: p, token: this.token})
        },
        cancelFunc() {
            if (this.cancel) {
                this.cancel()
            }
        },
        paymentCreate () {
            if (this.validate() && this.submitfunc) {
                this.submitfunc(this.item)
            }
        },
        paymentUpdate () {
            if (this.validate() && this.submitfunc) {
                this.submitfunc(this.item)
            }
        },
        loadAttachments () {
            if (this.item.attachments && this.item.attachments.length > 0) {
                this.attachments = []
                for (let i = 0; i < this.item.attachments.length; i++) {
                    getFile({token: this.token, id: this.item.attachments[i]}).then((res: any) => {
                        this.attachments.push(res['data'])
                    })
                }
            }
        }
    },
    data () {
        let errs: any = {
            paymentdate: undefined,
            amount: undefined
        }
        let attachments: any = []
        return {
            errs,
            basePath: `${basePath}api/upload`,
            attachments,
        }
    }
})
</script>
