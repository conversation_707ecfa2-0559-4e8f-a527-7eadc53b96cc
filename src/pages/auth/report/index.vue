<template>
    <div>
        <adminheader
            :title="$t('navigations.report')"></adminheader>
        <div class="mx-1 mr-2">
            <div class="w-full rounded bg-white p-3 shadow my-2">
                <template v-for="p in newReportList" :key="p.id" >
                  <router-link v-if="hasScopes(p.scopes)" :to="p.path" class="inline-block text-purple-800 w-32 h-32 mx-1 rounded border shadow px-2 py-3 cursor-pointer hover:bg-gray-100 hover:text-indigo-500">
                    <div  class="text-center">
                      <svgCollection :icon="p.icon" dclass="h-12 w-12 inline-block"></svgCollection>
                    </div>
                    <div class="text-center text-sm py-2 h-16 overflow-hidden">{{$t(p.title)}}</div>
                  </router-link>
                </template>
            </div>
            <div class="w-full rounded bg-white p-3 shadow my-2">
                <template v-for="p in reportlist" :key="p.id" >
                  <router-link v-if="hasScopes(p.scopes)" :to="p.path" class="inline-block text-purple-800 w-32 h-32 mx-1 rounded border shadow px-2 py-3 cursor-pointer hover:bg-gray-100 hover:text-indigo-500">
                    <div  class="text-center">
                      <svgCollection :icon="p.icon" dclass="h-12 w-12 inline-block"></svgCollection>
                    </div>
                    <div class="text-center text-sm py-2 h-16 overflow-hidden">{{$t(p.title)}}</div>
                  </router-link>
                </template>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  components: {
    adminheader,
    svgCollection,
  },
  methods: {
    hasScopes (p: any) {
      if (!p) {
        return true
      } else {
        let g: boolean = false
        if (this.scopes) {
          if (Array.isArray(p)) {
            for (let k = 0; k < this.scopes.length; k++) {
              if (p.indexOf(this.scopes[k]) > -1) {
                return true
              }
            }
            return false
          } else {
            return this.scopes.indexOf(p) > -1
          }          
        }
        return g
      }
    },
  },
  computed: {
    scopes () {
      let g: any = auth2Store.getState().profile
      return (g && g.scopes) || []
    },
    newReportList () {
      return [
        {
          id: 'billbydates',
          title: 'report.billbydate',
          icon: 'users',
          path: {name: 'billbydate'},
        },
        {
          id: 'billbyplan',
          title: 'report.billbyplan',
          icon: 'users',
          path: {name: 'billbyplan'},
        },
        {
          id: 'zerospecialratereport',
          title: 'report.zerospecialratereport',
          icon: 'report',
          path: {name: 'zerospecialratereport'},
        },
        {
          id: 'subscriptionreport',
          title: 'report.subscriptionreport',
          icon: 'report',
          path: {name: 'subscriptionreport'}
        },
      ]
    },
    reportlist () {
      return [
        {
          id: 'upcoming',
          title: 'report.upcomingbilling',
          icon: 'report',
          path: {name: 'upcomingreport'}
        },
        {
          id: 'jnxreport',
          title: 'report.jnxreport',
          icon: 'report',
          path: {name: 'jnxreport'},
          scopes: 'jnx'
        },
        {
          id: 'subpayreport',
          title: 'report.subpayreport',
          icon: 'report',
          path: {name: 'subpayreport'},
          scopes: ['dev','finance']
        },
        {
          id: 'activeuser',
          title: 'report.activeuser',
          icon: 'users',
          path: {name: 'activeuserreport'},
          scopes: 'dev'
        },
        {
          id: 'billwithagent',
          title: 'report.billwithagent',
          icon: 'users',
          path: {name: 'billwithagent'},
          scopes: 'dev'
        },
        {
          id: 'billwithagent2',
          title: 'report.billwithagent2',
          icon: 'users',
          path: {name: 'billwithagent2'},
          scopes: 'dev'
        },
        {
          id: 'adhocreport',
          title: 'report.adhocreport',
          icon: 'report',
          path: {name: 'adhocreport'}
        },
        {
          id: 'activationreport',
          title: 'report.activationreport',
          icon: 'report',
          path: {name: 'activationreport'}
        },
        {
          id: 'agentuserreport',
          title: 'report.agentuserreport',
          icon: 'report',
          path: {name: 'agentuserreport'}
        },
        {
          id: 'latestbillreport',
          title: 'report.latestbillreport',
          icon: 'report',
          path: {name: 'latestbillreport'}
        },
        {
          id: 'ticketreport',
          title: 'report.ticketreport',
          icon: 'report',
          path: {name: 'ticketreport'}
        },
        {
          id: 'ticketsummaryreport',
          title: 'report.ticketsummaryreport',
          icon: 'report',
          path: {name: 'ticketsummaryreport'}
        },
        {
          id: 'upcomingfreeusagereport',
          title: 'report.upcomingfreeusagereport',
          icon: 'report',
          path: {name: 'upcomingfreeusagereport'}
        },
        {
          id: 'salessummaryreport',
          title: 'report.salessummaryreport',
          icon: 'report',
          path: {name: 'salessummaryreport'}
        },
        {
          id: 'subscriptiontroubleshoot',
          title: 'report.subscriptiontroubleshoot',
          icon: 'report',
          path: {name: 'subscriptiontroubleshoot'}
        },
        // {
        //   id: 'userreport',
        //   title: 'report.userreport',
        //   icon: 'report',
        //   path: {name: 'userreport'}
        // }
        // {
        //   id: 'jnxreport',
        //   title: 'report.jnxreport',
        //   icon: 'report',
        //   path: {name: 'jnxreport'},
        //   scopes: 'jnx'
        // },
        // {
        //   id: 'upcoming',
        //   title: 'report.summarybills',
        //   icon: 'report',
        //   path: {name: 'summarybills'}
        // }
      ]
    }
  }
})
</script>