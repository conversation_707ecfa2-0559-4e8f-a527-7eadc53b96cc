<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('subscriptions.formTitle')"
      :btnNoText="$t('c.cancel')"
      :btnNoFunction="cancel"
      v-if="item">
    <div class="text-right" v-if="item.id">
      <!-- <div class="text-xs inline-block rounded px-2 py-1 text-white bg-gray-500">{{item.id}}</div> -->
    </div>
    <form @submit.prevent="preventsubmit" autocomplete="" v-if="showForm">
      <div class="border rounded p-2 mb-5 mt-2 text-right">
        <div @click="syncUser" class="text-xs rounded p-2 bg-gray-200 inline-block hover:bg-gray-500 cursor-pointer hover:text-white mr-1">AAA Sync</div>
        <div @click="dcUser" class="text-xs rounded p-2 bg-gray-200 inline-block hover:bg-gray-500 cursor-pointer hover:text-white mr-1">Disconnect User</div>
        <div @click="dsUser" class="text-xs rounded p-2 bg-gray-200 inline-block hover:bg-gray-500 cursor-pointer hover:text-white mr-1">Disable User</div>
        <div @click="enUser" class="text-xs rounded p-2 bg-green-200 inline-block hover:bg-green-500 cursor-pointer hover:text-white mr-1">Enable User</div>
        <div @click="updateUserPlan" class="text-xs rounded p-2 bg-purple-200 inline-block hover:bg-purple-500 cursor-pointer hover:text-white mr-1">Update Plan</div>
        <!-- <div @click="generatebill" class="text-xs rounded p-2 bg-blue-200 inline-block hover:bg-blue-500 cursor-pointer hover:text-white mr-1">Generate Bill</div>
        <div class="inline-block"><DatePicker v-model="genbilldate" defaultColor="blue"></DatePicker></div> -->
      </div>
      <div v-if="showbilldate && isDev">
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="sid"
            :labelTitle="$t('users.sid')">
              <TextInput
                name="sid"
                v-model="item.sid"
                readonly
                defaultColor="blue"></TextInput>
        </FormItemCompt>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="lastbilldate"
            :labelTitle="$t('subscriptions.lastbilldate')">
          <DatePicker
              v-model="item.lastbilldate"
              defaultColor="blue" disabled></DatePicker>
        </FormItemCompt>
      </div>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="plan"
          :labelTitle="$t('subscriptions.plan')"
          :required="true">
          <template v-if="showEquipments && planlist">
            <div class="bg-purple-700 rounded px-5 text-white text-lg inline-block">{{planTitle}}</div>
          </template>
      </FormItemCompt>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="plan"
          :labelTitle="$t('subscriptions.voip')">
          <template v-if="showEquipments">
            <div class="bg-purple-700 rounded px-5 text-white text-lg inline-block">{{voipTitle}}</div>
          </template>
      </FormItemCompt>
      <div class="">
        <div class="inline-block w-full lg:w-1/2">
          <div class="p-5"  v-if="!item.customer && !selectCustomer">
            <div class="inline-block w-1/2 px-1">
              <button  @click=" () => { selectCustomer = true; }" :class="btnClass1">Existing Customer</button>
            </div>
          </div>
          <FormItemCompt
            v-if="selectCustomer || item.customer"
            class="w-full inline-block px-1"
            labelFor="customer"
            :labelTitle="$t('subscriptions.applicant')"
            :required="true">
            <template  v-if="showEquipments">
            </template>
            <template v-else>
              <UserAssignInput
                  :token="token"
                  :addUser="addCustomer"
                  :searchUser="searchCustomer"
                  :users="customer"
                  :readonly="true"
                  :multiple="false"
                  :additionalParamsSearch="{customer: true, params: ['customer']}"
                  itemcls="w-full"
                  :plcHolder="$t('c.customerKeySearch')"
                  :noUserText="$t('c.nocustomer')"
                  :addText="$t('c.selectcustomer')"
                  defaultColor="blue"></UserAssignInput>
            <ErrTextCompt :errs="errs && errs.customer"></ErrTextCompt>
            </template>
          </FormItemCompt>
        </div>
        <template v-if="!showEquipments">
          <FormItemCompt v-if="!functionblock('saleschannel')"
            class="inline-block w-1/2 lg:w-1/4"
            labelFor="saleschannel"
            :labelTitle="$t('subscriptions.saleschannel')">
            <select v-model="item.saleschannel" class="p-4 w-full rounded">
              <option v-for="(p, g) in saleschannellist" :key="`saleschannel_${String(g)}`" :value="p">{{$t(`saleschannel.${p}`)}}</option>
            </select>
          </FormItemCompt>
          <div class="inline-block w-1/2 lg:w-1/4">
            <FormItemCompt              
                class="w-full inline-block px-1"
                labelFor="agent"
                :labelTitle="$t('subscriptions.agent')"
                :required="true">
                <UserAssignInput
                    :token="token"
                    :addUser="addAgent"
                    :searchUser="searchAgent"
                    :additionalParamsSearch="{agent: true, params: ['agent']}"
                    :users="agent"
                    itemcls="w-full"
                    :readonly="true"
                    :multiple="false"
                    :plcHolder="$t('subscriptions.agentKeySearch')"
                    :noUserText="$t('subscriptions.noagent')"
                    :addText="$t('subscriptions.selectagent')"
                    defaultColor="blue"></UserAssignInput>
              <ErrTextCompt :errs="errs && errs.agent"></ErrTextCompt>
            </FormItemCompt>
          </div>
        </template>        
      </div>
      <div v-if="customer && customer[0]" class="border rounded p-5">
        <div>
          <div class="pb-1 text-xs border-b mb-3">{{$t('subscriptions.applicantinfo')}}</div>
          <div class="text-sm" :key="`${String(i)}_cust`" v-for="(p,i) in custlist">
            <div class="inline-block w-full lg:w-1/3 pr-5" :key="`${String(i)}_${String(k)}_cust`" v-for="(q,k) in p">
              <div class="inline-block  border-b text-gray-500 w-2/5 pl-2">{{q.toUpperCase()}} </div>
              <div class="inline-block  border-b w-3/5">{{custvalue(q) || '&nbsp;'}}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="border rounded p-5 mt-5">
        <div>
          <div class="pb-1 text-xs font-bold border-b mb-3">{{$t('subscriptions.installtioninfo')}}</div>
            <FormItemCompt
              class="w-full inline-block px-1"
              labelFor="title"
              :labelTitle="$t('users.title')">
              <template v-if="titleString">
                <TextInput
                  name="title"
                  v-model="item.title"
                  :placeholder="$t('c.frontPlc') + $t('users.title')"
                  defaultColor="blue" readonly></TextInput>
              </template>
              <div v-else class="py-3">
                <SelectOne
                  :list="titleList"
                  :selectedItem="item.title"
                  showTitle="title"
                  itemValue="id"
                  defaultColor="blue" readonly></SelectOne>
              </div>
            <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full inline-block px-1"
              labelFor="name"
              :labelTitle="$t('users.name')"
              :required="true">
            <TextInput
                name="name"
                v-model="item.name"
                :placeholder="$t('c.frontPlc') + $t('users.name')"
                defaultColor="blue" readonly></TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/3 inline-block px-1"
              labelFor="email"
              :labelTitle="$t('users.email')"
              :required="true">
            <TextInput
                name="email"
                type="email"
                v-model="item.email"
                :placeholder="$t('c.frontPlc') + $t('users.email')"
                defaultColor="blue" readonly :required="true"></TextInput>
            <ErrTextCompt :errs="errs && errs.email"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="contact"
            :labelTitle="$t('users.contact')">
          <TextInput
              name="contact"
              type="text"
              v-model="item.contact"
              :placeholder="$t('c.frontPlc') + $t('users.contact')"
              defaultColor="blue" readonly :required="true"></TextInput>
          <ErrTextCompt :errs="errs && errs.contact"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="contact2"
            :labelTitle="$t('users.contact2')">
          <TextInput
              name="contact2"
              type="text"
              v-model="item.contact2"
              :placeholder="$t('c.frontPlc') + $t('users.contact2')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.contact2"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="voipno"
            :labelTitle="$t('users.voipno')">
          <TextInput
              name="voipno"
              type="text"
              v-model="item.voipno"
              :placeholder="$t('c.frontPlc') + $t('users.voipno')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.voipno"></ErrTextCompt>
        </FormItemCompt>
        </div>
      </div>
      <div class="border rounded p-5 mt-5">
        <div class="text-xs border-b font-bold mb-3 pb-1">
          {{$t('subscriptions.address')}}
        </div>
        <div>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="building"
            :labelTitle="$t('subscriptions.building')">
            <div>
              <select v-model="item.address['building']" class="p-4 rounded" disabled>
                  <option v-for="(p, g) in buildinglist" :key="`building_${String(g)}`" :value="p.key">{{p.title}}</option>
              </select>
            </div>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="block"
            :labelTitle="$t('subscriptions.block')">
            <TextInput
                name="block"
                type="text"
                v-model="item.address['block']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.block')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="level"
            :labelTitle="$t('subscriptions.level')">
            <TextInput
                name="level"
                type="text"
                v-model="item.address['level']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.level')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="unit"
            :labelTitle="$t('subscriptions.unit')">
            <TextInput
                name="unit"
                type="text"
                v-model="item.address['unit']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.unit')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>          
        </div>
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="address"
            :labelTitle="$t('subscriptions.address')"
            :required="true">
          <TextareaInput
              name="address"
              type="text"
              v-model="item.address['address']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.address')"
              :rows="2"
              defaultColor="blue" readonly></TextareaInput>
          <ErrTextCompt :errs="errs && errs.address"></ErrTextCompt>
        </FormItemCompt>
        <div>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="postcode"
            :labelTitle="$t('subscriptions.postcode')">
            <TextInput
                name="unit"
                type="text"
                v-model="item.address['postcode']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.postcode')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="town"
            :labelTitle="$t('subscriptions.town')">
            <TextInput
                name="town"
                type="text"
                v-model="item.address['city']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.town')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.state')">
            <TextInput
                name="state"
                type="text"
                v-model="item.address['state']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.state')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
        </div>
      </div>
      <div class="border rounded p-5 mt-5" v-if="!installerOnlyView">
        <div class="pb-1 border-b text-xs font-bold mb-3">
          {{$t('subscriptions.billadd')}}
          <button @click="copyaboveToBill" class="ml-5 text-xs bg-gray-300 rounded px-2 cursor-pointer">{{$t('subscriptions.sameasabove')}}</button>
        </div>
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="address"
            :labelTitle="$t('subscriptions.billadd')"
            :required="true">
          <TextareaInput
              name="address"
              type="text"
              v-model="item.billingaddress['address']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.billadd')"
              :rows="2"
              defaultColor="blue" readonly></TextareaInput>
          <ErrTextCompt :errs="errs && errs.billingaddress"></ErrTextCompt>
        </FormItemCompt>
        <div>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="postcode"
            :labelTitle="$t('subscriptions.postcode')">
            <TextInput
                name="unit"
                type="text"
                v-model="item.billingaddress['postcode']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.postcode')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="town"
            :labelTitle="$t('subscriptions.town')">
            <TextInput
                name="town"
                type="text"
                v-model="item.billingaddress['city']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.town')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.state')">
            <TextInput
                name="state"
                type="text"
                v-model="item.billingaddress['state']"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.state')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
        </div>
      </div>
      <div class="p-5 border rounded my-2">
        <div class="text-sm border-b font-bold mb-3">{{$t('subscriptions.specialprice')}}</div>
        <FormItemCompt
            class="w-full md:w-1/5 inline-block px-1"
            labelFor="price"
            :labelTitle="$t('subscriptions.price')">
            <TextInput
                name="price"
                type="number"
                v-model="item.price"
                :placeholder="$t('subscriptions.price')"
                defaultColor="blue" readonly></TextInput>
        </FormItemCompt>
      </div>  
      <div class="p-5 border rounded my-2" v-if="!showEquipments">
        <div class="text-sm border-b font-bold mb-3">{{$t('subscriptions.prepayment')}}</div>
        <FormItemCompt
            class="w-full md:w-1/5 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.deposit')">
            <TextInput
                name="deposit"
                type="number"
                v-model="item.deposit"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.deposit')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/5 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.advancedpayment')">
            <TextInput
              type="number"
                name="advancedpayment"
                v-model="item.advancedpayment"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.advancedpayment')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/5 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.freeusage')">
            <TextInput
              type="number"
                name="freeusage"
                v-model="item.freeusage"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.freeusage')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt
            class="w-full md:w-1/5 inline-block px-1"
            labelFor="state"
            :labelTitle="$t('subscriptions.installationfee')">
            <TextInput
              type="number"
                name="installationfee"
                v-model="item.installationfee"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.installationfee')"
                defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <div class="">
            <div class="text-xs inline-block w-full lg:w-1/4 text-gray-500">{{$t('subscriptions.prepaymenthints1')}}</div>
            <div class="text-xs inline-block w-full lg:w-1/4 text-gray-500">{{$t('subscriptions.prepaymenthints2')}}</div>
          </div>
          <div class="text-right">
            <div
              class="w-full md:w-1/5 inline-block px-1 pl-10 text-xs text-center">
              <div>{{$t('subscriptions.needToPay')}}</div>
              <div class="text-red-600 pt-3 text-2xl px-10">{{formatSum([item.deposit,item.advancedpayment])}}</div>
            </div>
            <div
              class="w-full md:w-1/5 inline-block px-1 pl-10 text-xs text-center">
              <div>{{$t('subscriptions.monthlypayment')}}</div>
              <div class="text-red-600 pt-3 text-2xl px-10">{{monthlyPaymentPrice}}</div>
            </div>
          </div>
      </div>
      <div class="p-8 mt-2 rounded border">
        <div>
          <div class="mb-2 border-b pb-1">{{$t('subscriptions.internet')}}</div>
          <FormItemCompt
              class="w-full md:w-1/2 inline-block px-1"
              labelFor="username"
              :labelTitle="$t('subscriptions.username')">
            <TextInput
                readonly
                name="username"
                type="text"
                v-model="item.username"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.username')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.username"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/2 inline-block px-1"
              labelFor="userpassword"
              :labelTitle="$t('subscriptions.userpassword')"
              :required="true">
            <TextInput
                name="userpassword"
                readonly
                type="text"
                v-model="item.userpassword"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.userpassword')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.userpassword"></ErrTextCompt>
          </FormItemCompt>
        </div>
        <div v-if="item.voip && regenerateUsername">
          <div class="mb-2 border-b pb-1">{{$t('subscriptions.voip')}}</div>
          <FormItemCompt
              class="w-full md:w-1/2 inline-block px-1"
              labelFor="voipusername"
              :labelTitle="$t('subscriptions.voipusername')">
            <TextInput
                readonly="true"
                name="voipusername"
                type="text"
                v-model="item.voipusername"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.voipusername')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.voipusername"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/2 inline-block px-1"
              labelFor="voipuserpassword"
              :labelTitle="$t('subscriptions.voipuserpassword')"
              :required="true">
            <TextInput
                name="voipuserpassword"
                readonly="true"
                type="text"
                v-model="item.voipuserpassword"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.voipuserpassword')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.voipuserpassword"></ErrTextCompt>
          </FormItemCompt>
        </div>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="subscribeDate"
            :labelTitle="$t('subscriptions.subscribeDate')">
          <DatePicker
              readonly="true"
              v-model="item.subscribedate"
              :clearable="clearabledate"
              defaultColor="blue" disabled></DatePicker>
        </FormItemCompt>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="activationDate"
            :labelTitle="$t('subscriptions.activationDate')">
          <DatePicker
              v-model="item.activationdate"
              :clearable="clearabledate"
              defaultColor="blue" disabled></DatePicker>
        </FormItemCompt>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="contractMonths"
            :labelTitle="$t('subscriptions.contractMonths')">
            <TextInput
                name="contractMonths"
                type="number"
                v-model="item.contractmonths"
                :placeholder="$t('c.frontPlc') + $t('subscriptions.contractMonths')"
                defaultColor="blue" readonly></TextInput>
        </FormItemCompt>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="contractEndDate"
            :labelTitle="$t('subscriptions.contractEndDate')">
          <DatePicker
              v-model="item.contractenddate"
              :clearable="clearabledate"
              defaultColor="blue" disabled></DatePicker>
        </FormItemCompt>
        <FormItemCompt
            class="w-1/2 lg:w-1/5 inline-block px-1"
            labelFor="terminationDate"
            :labelTitle="$t('subscriptions.terminationDate')">
          <DatePicker
              v-model="item.terminationdate"
              :clearable="clearabledate"
              defaultColor="blue" disabled></DatePicker>
        </FormItemCompt>
      </div>
      <FormItemCompt class="my-5"
          v-if="!installerOnlyView"
          labelFor="attachments"
          :labelTitle="$t('subscriptions.attachments')">
            <div>
                <ShowFiles :files="attachments" />
            </div>
      </FormItemCompt>
      <FormItemCompt v-if="!installerOnlyView"
          class="w-full inline-block px-1"
          labelFor="remark"
          :labelTitle="$t('subscriptions.remark')">
        <TextareaInput
            name="remark"
            readonly
            v-model="item.remark"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.remark')"
            :rows="6"
            defaultColor="blue"></TextareaInput>
      </FormItemCompt>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="installationremark"
          :labelTitle="$t('subscriptions.installationremark')">
        <TextareaInput
            name="installationremark"
            readonly
            v-model="item.installationremark"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.installationremark')"
            :rows="6"
            defaultColor="blue"></TextareaInput>
      </FormItemCompt>
      <div class="my-5"> 
        <!-- profile.installer || showEquipments -->
        <div class="mb-2 border rounded pb-1">
          <div>
            <FormItemCompt
              class="w-1/3 lg:w-1/4 inline-block px-1"
              labelFor="port"
              :labelTitle="$t('subscriptions.port')">
              <TextInput
                  name="port"
                  readonly
                  v-model="item.port"
                  :placeholder="$t('c.frontPlc') + $t('subscriptions.port')"
                  defaultColor="blue"></TextInput>
            </FormItemCompt>
            <FormItemCompt
              class="w-1/3 lg:w-1/4 inline-block px-1"
              labelFor="circuit"
              :labelTitle="$t('subscriptions.splitter')">
              <TextInput
                  name="splitter"
                  readonly
                  v-model="item.splitter"
                  :placeholder="$t('c.frontPlc') + $t('subscriptions.splitter')"
                  defaultColor="blue"></TextInput>
            </FormItemCompt>
            <FormItemCompt
              class="w-1/3 lg:w-1/4 inline-block px-1"
              labelFor="serial"
              :labelTitle="$t('subscriptions.serial')">
              <TextInput
                  name="serial"
                  readonly
                  v-model="item.serial"
                  :placeholder="$t('c.frontPlc') + $t('subscriptions.serial')"
                  defaultColor="blue"></TextInput>
            </FormItemCompt>

            <FormItemCompt
              class="w-1/3 lg:w-1/4 inline-block px-1"
              labelFor="gponsn"
              :labelTitle="$t('subscriptions.gponsn')">
              <TextInput
                  name="gponsn"
                  readonly
                  v-model="item.gponsn"
                  :placeholder="$t('c.frontPlc') + $t('subscriptions.gponsn')"
                  defaultColor="blue"></TextInput>
            </FormItemCompt>
          </div>          
        </div>
      </div> 
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="statustxt"
          :labelTitle="$t('subscriptions.statusTxt')">
          <select v-model="item.statustxt" class="form-select p-2 rounded text-lg block w-full" disabled>
            <option v-for="(status, index) in statuses" :key="`${String(index)}_status`" :value="String(status)">{{$t(`subscriptions.${status}`)}}</option>
          </select>
      </FormItemCompt>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import axios from 'axios'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import UserAssignInput from '@/components/cvui/form/UserAssignInput.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import UploadInput from '@/components/cvui/form/Upload2.vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import dtable from '@/components/cvui/table/index.vue'
import { updateUserDMAPlan, getNextSubscriptionID, pushSubscriptionID, getDMAUserCheck, generatebillspecific, createDMAUser, getPlans, getPlan, getFile, deleteFile, basePath, searchUser, createUser, getUserName, disableUser, disconnectUser, enableUser, getBuildings } from '../../../api'
import svgcollection from '@/components/cvui/svgcollection.vue'
import userform from '../users/userform.vue'
import moment from 'moment'
import config from '../../../config'

export default defineComponent({
  props: {
    profile: {
      type: Object,
      required: true,
    },
    item: {
      type: Object,
      required: true
    },
    cancel: {
      type: Function,
      required: true
    },
    save: {
      type: Function,
      required: true
    },
    saveSilent: {
      type: Function,
      required: true
    },
    token: {
      type: String,
      required: true
    },
    showEquipments: {
      type: Boolean,
      default: false
    },
    installerOnlyView: {
      type: Boolean,
      default: false
    } // #TODO control what installer need to view
  },
  components: {
    // Vue3Html2pdf,
    PopupModal,
    FormItemCompt,
    TextInput,
    TextareaInput,
    UserAssignInput,
    SelectOne,
    DatePicker,
    ErrTextCompt,
    UploadInput,
    dtable,
    svgcollection,
    userform,
    ShowFiles,
  },
  methods: {
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {     
        if (this.cancel){
          this.cancel()
        }   
      }
    },
    generateSID () {
      if (!this.item.sid) {
        getNextSubscriptionID({token: this.token}).then((res: any) => {
          this.item.sid = res.data
          pushSubscriptionID({token: this.token})
        })
      }
    },
    functionblock(p:  any) {
      if (config && config.functionsblock && config.functionsblock.indexOf(p) > -1) {
        return true
      } else {
        return false
      }
    },
    copyaboveToBill () {
      this.item.billingaddress = JSON.parse(JSON.stringify(this.item.address))
      if (!this.item.billingaddress) {
        this.item.billingaddress = {
          address: '',
          city: '',
          state: '',
          postcode: ''
        }
      }
      if (this.item.billingaddress["unit"]) {
        delete this.item.billingaddress["unit"]
      }
      if (this.item.billingaddress["level"]) {
        delete this.item.billingaddress["level"]
      }
      if (this.item.billingaddress["block"]) {
        delete this.item.billingaddress["block"]
      }
      if (this.item.billingaddress["building"]) {
        delete this.item.billingaddress["building"]
      }
    },
    autoUsername () {
      if (!this.item.username || this.item.username.length == 0) {
        let us = this.item.name.replace(/\s/g, '').toLowerCase() + '_' + this.randomNum(2)
        let ps = this.randomPass(8)
        this.item.username =  us + '@highfi'
        this.item.userpassword = ps

        if (this.item.voip) {
          this.item.voipusername = us + '_voip' + '@highfi'
          this.item.voipuserpassword = ps
        }
      }
    },
    generatebill () {
      if (confirm('Are you sure to generate bill?')) {
        generatebillspecific({token: this.token, id: this.item.id, date: moment(this.genbilldate).format('DD-MM-YYYY')}).then((res: any) => {
          alert('bill generated')
        })
      }
    },
    autoUsername2 () {
      if (!this.item.username || this.item.username.length == 0) {
        this.item.username = ''
        this.item.userpassword = ''
        this.item.voipusername = ''
        this.item.voipuserpassword = ''
        this.regenerateUsername = false

        let p1 = 'b' + (this.item.address.block || '0') + '_l' +  (this.item.address.level || '0') + '_u' +  (this.item.address.unit || '0')
        let us = p1.toLowerCase() + '_' + this.randomNum(3)
        let ps = '1' + p1.toLowerCase().replace(/_/g, '')        
        this.$nextTick(() => {
          this.item.username = us + '@highfi'
          this.item.userpassword = ps

          if (this.item.voip != null) {
            this.item.voipusername = us + '_voip' + '@highfi'
            this.item.voipuserpassword = ps
          }
          this.regenerateUsername = true
        })
      }
    },
    clearUsernamePassword () {
      if (confirm(this.$t('subscriptions.confirmClear'))) {
        this.$nextTick(()=> {
          this.item.username = ''
          this.item.userpassword = ''
        })
      }
    },
    closeCreateCustomer () {
      this.customerItem = undefined
    },
    autoEndDate () {
      if (this.item.activationdate) {
        this.item.contractenddate = moment(this.item.activationdate).add(this.item.contractmonths, 'months').toDate()
      }
    },
    validateUser() {
      let p = true
        if (!this.customerItem.email || this.customerItem.email.trim().length == 0) {
            this.usererrs['email'] = 'c.fieldRequired'
            p = false
        }
        if (!this.customerItem.name || this.customerItem.name.trim().length == 0) {
            this.usererrs['name'] = 'c.fieldRequired'
            p = false
        }
        if (!this.customerItem.identityno || this.customerItem.identityno.trim().length == 0) {
            this.usererrs['identityno'] = 'c.fieldRequired'
            p = false
        }
        return p
    },
    formatSum(p: any) {
      let r = 0
      if (p) {
        p.forEach((g: any) => {
          if (!isNaN(g)) {
            r += parseFloat(g)
          }
        })
      }
      return r.toFixed(2)
    },
    // monthlyPaymentPrice () {
    //   var r  = 0
    //   if (this.item.plan) {
    //     let g = this.planlist.find((p: any) => p.id == this.item.plan)
    //     if (g) {
    //       r = g.price
    //     }
    //   }
    //   if (this.item.voip) {
    //     let g = this.voiplist.find((p: any) => p.id == this.item.voip)
    //     if (g) {
    //       r += g.price
    //     }
    //   }
    //   if (this.item.price) {
    //     r = this.item.price
    //   }
    //   return r.toFixed(2)
    // },
    randomPass (n: number) : string {
      let chars = 'abcdefghjkmnpqrstuvwxyz0123456789'
      let pass = ''
      for (let x = 0; x < n; x++) {
        let i = Math.floor(Math.random() * chars.length)
        pass += chars.charAt(i)
      }
      return pass
    },
    randomNum (n: number) : string {
      let chars = '0123456789'
      let pass = ''
      for (let x = 0; x < n; x++) {
        let i = Math.floor(Math.random() * chars.length)
        pass += chars.charAt(i)
      }
      return pass
    },
    createCustomerNow () {
      if (this.validateUser()) {
        createUser(Object.assign({token: this.token}, {form: Object.assign({password: this.randomPass(8)}, this.customerItem)})).then((res: any) => {
          if (res && res.data && res.data.id) {
            axios.get("https://ktic.com.my/api/hi5_status_update.php", {
              params: {
                security_token: "KTIC20240625-17678-897KBVHJV",
                order_no: res.data.sid,
                status: res.data.statustxt
              }
            })
            this.customerItem = undefined
            this.item.customer = res.data.id
            this.customer.push(res.data)
          }
        }).catch((err:any) => {
          alert('User Email Existed. Please try another.')
        })
      }
    },
    createCustomer () {
      this.selectCustomer = false
      this.customerItem = {
        email: '',
        identityNo: '',
        mobile: '',
        name: '',
        nationality: 'MY',
        gender: true,
        customer: true,
        mothersName: '',
        fathersName: '',
        scopes: [],
        status: true
      }
    },
    saveUser (item: any) {
      this.customerItem = undefined
      this.loadCustAgent()
    },
    validateForm () {
      let p: any = true
      if (!this.item.customer || this.item.customer.trim().length == 0) {
        this.errs['customer'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.plan || this.item.plan.trim().length == 0) {
        this.errs['plan'] = 'c.fieldRequired'
        p = false
      }
      if (this.item.price) {
        this.item.price = parseFloat(this.item.price)
      }
      // if (this.item.deposit && this.item.deposit.trim().length > 0 && !isNaN(this.item.deposit)) {
      //   this.item.deposit = parseFloat(this.item.deposit)
      // } else {
      //   this.item.deposit = 0
      // }
      // if (this.item.tax) {
      //   this.item.tax = parseFloat(this.item.tax)
      // }
      return p
    },
    preventsubmit (e: any) {
      e.preventDefault()
    },
    submitNow (e: any) {
      // e.preventDefault()
      if (this.validateForm()) {
        this.save(this.item)
      }
      return false
    },
    // start customer
    addCustomer (p: any) {
      if (p) {
        if (!this.item.customer) {
            this.item.customer = ''
        }
        if (this.item.customer !== p.ID) {
            this.item.customer = p.ID
            this.customer.push(p)
        }
        this.showCustomer = true
      }        
    },
    removeCustomer (p: any) {
      if (p) {
        this.customer = []
        this.item.customer = ''
      }        
    },
    // end customer
    // start agent
    addAgent (p: any) {
        if (!this.item.agent) {
            this.item.agent = ''
        }
        if (this.item.agent !== p.ID) {
            this.item.agent = p.ID
            this.agent.push(p)
        }
        this.showAgent = true
    },
    removeAgent (p: any) {
        if (this.item.agent === p.ID || this.item.agent === p.id) {
            this.item.agent = ''
            this.agent = []
        }
    },
    removeDuplicates(array: any) {
      let uniqueObjects: any = [];
      let ids = new Set();
      array.forEach((obj: any) => {
          if (!ids.has(obj.id)) {
              ids.add(obj.id);
              uniqueObjects.push(obj);
          }
      });
      return uniqueObjects;
    },
    // end agent
    // start plan
    loadplan (pg: any, pageSize: any, keywords: any) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      getPlans({ token: this.token, voip: false, skip: skip, limit: pageSize, keywords: keywords }).then(res => {
        var d : any = res
        if (d.data) {
          this.planlist = this.planlist.concat(d.data)
          this.planlist = this.removeDuplicates(this.planlist)          
        }
      })
    },
    loadvoip (pg: any, pageSize: any, keywords: any) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      getPlans({ token: this.token, voip: true, skip: skip, limit: pageSize, keywords: keywords }).then(res => {
        var d : any = res
        if (d.data) {
          this.voiplist = this.voiplist.concat(d.data)
          if ((d.total / pageSize) >= pg) {
            // #TODO @wk check this
            // loadplan(pg + 1, pageSize, keywords)
          }
        }
      })
    },
    showSelectPlan () {
      this.selectPlanShow = true  
    },
    searchPlan () {
      this.loadplan(1, 10, this.planKeywrd)
    },
    addRemovePlan (item: any, index: any) {
      if (this.item.plan === item.id) {
        this.item.plan = ''
      } else {
        this.item.plan = item.id
      }
    },
    removePlan () {
      this.item.plan = ''
    },
    addRemoveVoip (item: any, index: any) {
      if (this.item.voip === item.id) {
        this.item.voip = ''
      } else {
        this.item.voip = item.id
      }
    },
    // updatePlan () {
    //   // this.item.plan
    //   let planID: string = ''
    //   getPlan({token: this.token, id: this.item.plan}).then((res: any) => {
    //     if (res) {
    //       updateUserPlan({token: this.token, form: {username: this.item.username, plan: planID}}).then((res: any) => {
    //         alert('plan updated')
    //       })
    //     }
        
    //   })
      
    // },
    // end plan
    // start username
    usernameAvailabilityFunc () {
      console.log(this.item.username)
      // #TODO check username
    },
    // end username
    // start attachments
    addFile (p: any) {
      if (!this.item.attachments) {
      this.item.attachments = []
      }
      this.item.attachments.push(p.file.id)
      this.attachments.push(p.file)
      this.silentSaveFix()
    },
    silentSaveFix () {
      //this.item = JSON.parse(JSON.stringify(this.item))
      //this.saveSilent(this.item)
      this.saveSilent()
    },
    removeFile (p: any) {
      let i = this.item.attachments.indexOf(p)
      if (i > -1) {
        this.item.attachments.splice(i, 1)
        this.silentSaveFix()
        // this.saveSilent(this.item)
      }
      let list = this.attachments.map((p: any) => p.id || p.ID)
      let j = list.indexOf(p)
      this.attachments.splice(j, 1)
      deleteFile({id: p, token: this.token})
    },
    dcUser () {
      if (this.item.username) {
        disconnectUser({token: this.token, data:{ id: this.item.username }})
        alert('user disconnected.');
      }
    },
    dsUser () {
      if (this.item.username) {
        disableUser({token: this.token, data:{ id: this.item.username }})
        alert('user disabled');
      }
    },
    enUser () {
      if (this.item.username) {
        enableUser({token: this.token, data:{ id: this.item.username }})
        alert('user enabled');
      }
    },
    loadCustAgent () {
      this.customer = []
      this.agent = []
      if (this.item.customer && this.item.customer.trim().length > 0) {
        getUserName({token: this.token, id: this.item.customer}).then(res => {
          this.customer.push(res)
        })
      }
      if (this.item.agent && this.item.agent.trim().length > 0) {
        getUserName({token: this.token, id: this.item.agent}).then(res => {
          this.agent.push(res)
        })
      }
    },
    valuec (c:any, q: string) {
      const cc = config.countrieswithcode
      if (q == 'dob') {
        return moment(c).format('DD/MM/YYYY')
      } else if (q == 'title') {
        return (c && c.toUpperCase()) || ''
      } else if (q == 'nationality') {
        return cc.find((p: any) => c == p.code)?.name
      } else if (q == 'gender') {
        console.log(c)
        return c == false ? this.$t('c.female') : this.$t('c.male')
      } else {
        return c
      }
    },
    custvalue (q: any) {
      if (this.customer && this.customer.length > 0) {
        const custid = this.item.customer
        let c = this.customer.find((p: any) => custid == (p.id || p.ID))
        if (c) {
          return this.valuec(c[q], q)
        }
      } else {
        return false
      }
    },
    switchTitleString() {
        this.titleString = !this.titleString
    },
    selectTitle (item: any, index: any) {
        if (this.item.title === item.id) {
            this.item.title = ''
        } else {
            this.item.title = item.id
        }
    },
    copyabove (e: any) {
      e.preventDefault()
      const p = this.customer.find((g: any) => (g.id || g.ID) == this.item.customer)
      if (p) {        
        this.item.title = p.title || ''
        this.item.email = p.email || ''
        this.item.name = p.name || ''
        this.item.contact = p.contact || ''
        this.item.contact2 = p.contact2 || ''
        this.refreshForm()
      }
    },
    refreshForm () {
      this.showForm = false
      this.$nextTick(() => {
        this.showForm = true
      })
    },
    syncUser () {
      // check if username exist in DMA
      getDMAUserCheck({token: this.token, username: this.item.username}).then((res: any) => {
        if (res) {
          if (res.data && res.data == "username available") {
            if (confirm('Are you sure to sync & create user?')) {
              createDMAUser({token: this.token, id: this.item.id}).then((res: any) => {
                if (res.data.affectedRows == 1) {
                  alert('user created')
                }
              })
            }
          }
        }
      }).catch((err: any) => {
        // console.log(err.response.data)
        if (err.response.data && err.response.data.data  && err.response.data.data == "username already occupied") {
          if (confirm('Username already existed in DMA\nDo you wish to sync the plan as well?')) {
            updateUserDMAPlan({token: this.token, id: this.item.id}).then((res: any) => {
              if (res.data.affectedRows == 1) {
                  alert('user plan updated')
                }
            })
          }
        }
      })
    },
    printInstallOrder () {
      // alert(this.$router.resolve({name: 'printInstallOrder', params: {id: this.item.id}}).href)
      window.open(this.$router.resolve({name: 'printInstallOrder', params: {id: this.item.id}}).href, '_blank')
    },
    updateUserPlan () {
      updateUserDMAPlan({token: this.token, id: this.item.id}).then((res: any) => {
        if (res.data.affectedRows == 1) {
          alert('user plan updated')
        }
      })
    },
    loadAttachments () {
      if (this.item.attachments && this.item.attachments.length > 0) {
        this.attachments = []
        for (let i = 0; i < this.item.attachments.length; i++) {
          getFile({token: this.token, id: this.item.attachments[i]}).then((res: any) => {
            this.attachments.push(res['data'])
          })
        }
      }
    },
    loadBuilding(pg: number, pageSize: number, keywords: string) {
      const fetchData = (skip: number, limit: number) => {
        getBuildings({ token: this.token, skip, limit, keywords }).then((res: any) => {
          if (res.data) {            
            this.buildinglist = this.buildinglist.concat(res.data);
            this.buildinglist = this.removeDuplicates(this.buildinglist);

            const totalRetrieved = this.buildinglist.length;
            const totalAvailable = res.total;

            if (totalRetrieved < totalAvailable) {
              fetchData(totalRetrieved, limit);
            }
          }
        });
      };

      const initialSkip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0;
      fetchData(initialSkip, pageSize);
    },
  },
  mounted () {
    document.addEventListener('keydown', this.handleEscKey);
    this.loadplan(1, 10, '')
    this.loadvoip(1, 10, '')
    this.loadCustAgent()
    this.loadAttachments()
    this.loadBuilding(1, 10, '')
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  data () {
    let showForm: Boolean = true
    let errs: any = {}
    let usererrs: any = {}
    let customer : any = []
    let agent : any = []
    let planlist : any = []
    let voiplist: any = []
    let attachments: any = []
    let genbilldate: any = moment()
    let regenerateUsername: Boolean = true
    let equipmentItemStyle: any = {
      name: '',
      serial: '',
      brand: '',
      model: '',
      type: '',
      status: ''
    }
    let equipmentItem: any = undefined
    let errsEquipments: any = {}
    let showCustomer: Boolean = false
    let showAgent: Boolean = false
    let customerItem: any = undefined
    let selectCustomer: Boolean = false
    let customerdb: any = undefined
    let titleString = false
    let titleList: any = config.titles
    
    let btnClass1: string = 'cursor-pointer bg-gray-300 py-2 hover:bg-purple-600 hover:text-white rounded w-full'
    const custlist: any = [
      ["title", "name", "identityno"],
      ["email", "dob", "nationality"],
      ["gender", "contact", "contact2"]
    ]
    const statuses:any = config.subscriptionStatus
    let clearabledate: Boolean = true
    let showSuggestUsername: Boolean = false
    let showbilldate: Boolean = true
    let buildinglist: any = []
    let saleschannellist: any = ['direct', 'agent', 'dealer']
    let selectPlanShow: Boolean = false
    let planKeywrd: string = ''
    return {
      showForm,
      planKeywrd,
      selectPlanShow,
      showSuggestUsername,
      regenerateUsername,
      errs,
      genbilldate,
      clearabledate,
      usererrs,
      customer,
      agent,
      planlist,
      voiplist,
      attachments,
      basePath: `${basePath}api/upload`,
      equipmentItemStyle,
      equipmentItem,
      errsEquipments,
      showCustomer,
      showAgent,
      btnClass1,
      customerdb,
      searchCustomer:searchUser,
      searchAgent:searchUser,
      customerItem,
      selectCustomer,
      titleString,
      titleList,
      custlist,
      statuses,
      showbilldate,
      buildinglist,
      saleschannellist,
    }
  },
  computed: {
    filteredPlans () {
      return this.planlist.filter((p: any) => p.title.indexOf(this.planKeywrd) > -1)
    },
    monthlyPaymentPrice () {
      var r  = 0
      if (this.item.plan) {
        let g = this.planlist.find((p: any) => p.id == this.item.plan)
        if (g) {
          r = g.price
        }
      }
      if (this.item.voip) {
        let g = this.voiplist.find((p: any) => p.id == this.item.voip)
        if (g) {
          r += g.price
        }
      }
      if (this.item.price) {
        r = this.item.price
        if (typeof r == 'string') {
          r = parseFloat(r)
        }
      }
      return r.toFixed(2)
    },
    isDev (): boolean {
      return this.profile && this.profile.scopes && this.profile.scopes.indexOf('dev') > -1
    },
    findPlan ():any {
      let x = this.planlist && this.planlist.find((p: any) => p.id == this.item.plan)
      return (x && x.title) || ''
    },
    planTitle () {
      let t: String = ''
      if (this.item.plan) {
        t = this.planlist.find((p: any) => p.id == this.item.plan)?.title
      }
      return t
    },
    voipTitle () {
      // voiplist.find((p: any) => p["id"] == item["voip"])?.title
      let t: String = ''
      if (this.item.voip) {
        t = this.voiplist.find((p: any) => p.id == this.item.voip)?.title
      }
      return t
    },
    equipmentColumns () {
      return [
        { title: 'subscriptions.name', key: 'name', type: 'string', class: 'text-center' },
        { title: 'subscriptions.serial', key: 'serial', type: 'string', class: 'text-center' },
        { title: 'subscriptions.brand', key: 'brand', type: 'string', class: 'text-center' },
        { title: 'subscriptions.model', key: 'model', type: 'string', class: 'text-center' },
        { title: 'subscriptions.type', key: 'type', type: 'string', class: 'text-center' },
        { title: 'subscriptions.status', key: 'status', type: 'string', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    }
  }
})
</script>
